package com.yf.exam.modules.weather.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.modules.user.UserUtils;
import com.yf.exam.modules.weather.dto.WeatherHistoryExamAnswerDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamAnswerReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamAnswerSaveReqDTO;
import com.yf.exam.modules.weather.dto.request.WeatherHistoryExamSubmitReqDTO;
import com.yf.exam.modules.weather.entity.WeatherHistoryExamAnswer;
import com.yf.exam.modules.weather.mapper.WeatherHistoryExamAnswerMapper;
import com.yf.exam.modules.weather.service.WeatherHistoryExamAnswerService;
import com.yf.exam.modules.weather.service.WeatherGradingConfigService;
import com.yf.exam.modules.weather.scoring.engine.WeatherScoringEngine;
import com.yf.exam.modules.weather.scoring.engine.ScoringEngineResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * <p>
 * 历史个例考试答案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class WeatherHistoryExamAnswerServiceImpl extends ServiceImpl<WeatherHistoryExamAnswerMapper, WeatherHistoryExamAnswer>
        implements WeatherHistoryExamAnswerService {

    @Autowired
    private WeatherGradingConfigService gradingConfigService;

    @Autowired
    private WeatherScoringEngine scoringEngine;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WeatherHistoryExamAnswerDTO saveAnswer(WeatherHistoryExamAnswerSaveReqDTO reqDTO) {
        String currentUserId = UserUtils.getUserId(true);
        Date now = new Date();

        WeatherHistoryExamAnswer answer;

        // 如果有answerId，则更新现有答案
        if (StringUtils.hasText(reqDTO.getAnswerId())) {
            answer = this.getById(reqDTO.getAnswerId());
            if (answer == null) {
                throw new RuntimeException("答案记录不存在");
            }
            // 检查权限
            if (!currentUserId.equals(answer.getUserId())) {
                throw new RuntimeException("无权限修改此答案");
            }
        } else {
            // 查询是否已存在答案记录
            QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .eq(WeatherHistoryExamAnswer::getExamId, reqDTO.getExamId())
                    .eq(WeatherHistoryExamAnswer::getQuestionId, reqDTO.getQuestionId())
                    .eq(WeatherHistoryExamAnswer::getUserId, currentUserId);

            answer = this.getOne(wrapper);
            if (answer == null) {
                // 创建新答案记录
                answer = new WeatherHistoryExamAnswer();
                answer.setExamId(reqDTO.getExamId());
                answer.setQuestionId(reqDTO.getQuestionId());
                answer.setUserId(currentUserId);
                answer.setAnswerStatus(0); // 答题中
                answer.setGradingStatus(0); // 未批改
                answer.setCreateTime(now);
            }
        }

        // 更新答案数据
        log.info("=== 保存答案数据调试 ===");
        log.info("reqDTO.getPrecipitationAnswer(): {}", reqDTO.getPrecipitationAnswer());
        log.info("reqDTO.getWeatherAnswer(): {}", reqDTO.getWeatherAnswer());
        log.info("reqDTO.getOverallProgress(): {}", reqDTO.getOverallProgress());

        answer.setPrecipitationAnswer(reqDTO.getPrecipitationAnswer());
        answer.setWeatherAnswer(reqDTO.getWeatherAnswer());
        answer.setOverallProgress(reqDTO.getOverallProgress());
        answer.setTimeSpent(reqDTO.getTimeSpent());
        answer.setUpdateTime(now);



        // 解析答题时间
        if (StringUtils.hasText(reqDTO.getAnswerTime())) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                answer.setAnswerTime(sdf.parse(reqDTO.getAnswerTime()));
            } catch (Exception e) {
                log.warn("解析答题时间失败: {}", reqDTO.getAnswerTime(), e);
                answer.setAnswerTime(now);
            }
        } else {
            answer.setAnswerTime(now);
        }

        // 保存或更新
        this.saveOrUpdate(answer);

        log.info("保存历史个例考试答案成功，用户ID: {}, 考试ID: {}, 题目ID: {}, 答案ID: {}",
                currentUserId, reqDTO.getExamId(), reqDTO.getQuestionId(), answer.getId());

        return BeanMapper.map(answer, WeatherHistoryExamAnswerDTO.class);
    }

    @Override
    public WeatherHistoryExamAnswerDTO getAnswer(WeatherHistoryExamAnswerReqDTO reqDTO) {
        String currentUserId = UserUtils.getUserId(true);

        log.info("=== 获取答案数据调试 ===");
        log.info("examId: {}, questionId: {}, userId: {}", reqDTO.getExamId(), reqDTO.getQuestionId(), currentUserId);

        QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(WeatherHistoryExamAnswer::getExamId, reqDTO.getExamId())
                .eq(WeatherHistoryExamAnswer::getQuestionId, reqDTO.getQuestionId())
                .eq(WeatherHistoryExamAnswer::getUserId, currentUserId);

        WeatherHistoryExamAnswer answer = this.getOne(wrapper);
        if (answer == null) {
            log.info("=== 未找到答案记录 ===");
            return null;
        }

        log.info("=== 从数据库查询到的答案数据 ===");
        log.info("answer.getId(): {}", answer.getId());
        log.info("answer.getPrecipitationAnswer(): {}", answer.getPrecipitationAnswer());
        log.info("answer.getWeatherAnswer(): {}", answer.getWeatherAnswer());
        log.info("answer.getOverallProgress(): {}", answer.getOverallProgress());

        WeatherHistoryExamAnswerDTO dto = BeanMapper.map(answer, WeatherHistoryExamAnswerDTO.class);

        log.info("=== BeanMapper映射后的DTO数据 ===");
        log.info("dto.getId(): {}", dto.getId());
        log.info("dto.getPrecipitationAnswer(): {}", dto.getPrecipitationAnswer());
        log.info("dto.getWeatherAnswer(): {}", dto.getWeatherAnswer());
        log.info("dto.getOverallProgress(): {}", dto.getOverallProgress());

        return dto;
    }

    /**
     * 检查用户是否已经参加过指定考试
     * @param examId 考试ID
     * @return 考试状态信息，如果没有参加过返回null
     */
    public WeatherHistoryExamAnswerDTO checkExamStatus(String examId) {
        String currentUserId = UserUtils.getUserId(true);

        QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(WeatherHistoryExamAnswer::getExamId, examId)
                .eq(WeatherHistoryExamAnswer::getUserId, currentUserId);

        WeatherHistoryExamAnswer answer = this.getOne(wrapper);
        if (answer == null) {
            return null;
        }

        return BeanMapper.map(answer, WeatherHistoryExamAnswerDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitExam(WeatherHistoryExamSubmitReqDTO reqDTO) {
        String currentUserId = UserUtils.getUserId(true);
        Date now = new Date();

        WeatherHistoryExamAnswer answer;

        // 如果有answerId，直接获取
        if (StringUtils.hasText(reqDTO.getAnswerId())) {
            answer = this.getById(reqDTO.getAnswerId());
        } else {
            // 根据考试ID和题目ID查询
            QueryWrapper<WeatherHistoryExamAnswer> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .eq(WeatherHistoryExamAnswer::getExamId, reqDTO.getExamId())
                    .eq(WeatherHistoryExamAnswer::getQuestionId, reqDTO.getQuestionId())
                    .eq(WeatherHistoryExamAnswer::getUserId, currentUserId);
            answer = this.getOne(wrapper);
        }

        if (answer == null) {
            // 如果没有找到答案记录，创建一个新的记录
            log.info("未找到答案记录，创建新的答案记录进行提交，用户ID: {}, 考试ID: {}, 题目ID: {}",
                    currentUserId, reqDTO.getExamId(), reqDTO.getQuestionId());

            answer = new WeatherHistoryExamAnswer();
            answer.setExamId(reqDTO.getExamId());
            answer.setQuestionId(reqDTO.getQuestionId());
            answer.setUserId(currentUserId);
            answer.setAnswerStatus(0); // 答题中
            answer.setGradingStatus(0); // 未批改
            answer.setCreateTime(now);

            // 如果前端传递了答案数据，使用它；否则设置为空
            if (reqDTO.getAnswerData() != null) {
                try {
                    // 将answerData转换为JSON字符串并解析
                    String answerDataJson = JSON.toJSONString(reqDTO.getAnswerData());
                    JSONObject answerDataObj = JSON.parseObject(answerDataJson);

                    // 设置降水预报答案
                    if (answerDataObj.containsKey("precipitationAnswer")) {
                        JSONObject precipitationObj = answerDataObj.getJSONObject("precipitationAnswer");
                        answer.setPrecipitationAnswer(precipitationObj.getInnerMap());
                    }

                    // 设置天气预报答案
                    if (answerDataObj.containsKey("weatherAnswer")) {
                        JSONObject weatherObj = answerDataObj.getJSONObject("weatherAnswer");
                        answer.setWeatherAnswer(weatherObj.getInnerMap());
                    }

                    // 设置进度和用时
                    if (answerDataObj.containsKey("overallProgress")) {
                        answer.setOverallProgress(answerDataObj.getInteger("overallProgress"));
                    }
                    if (answerDataObj.containsKey("timeSpent")) {
                        answer.setTimeSpent(answerDataObj.getInteger("timeSpent"));
                    }

                    log.info("从提交数据中设置答案内容成功");
                } catch (Exception e) {
                    log.warn("解析提交的答案数据失败: {}", e.getMessage(), e);
                    // 解析失败时设置为空
                    answer.setPrecipitationAnswer(null);
                    answer.setWeatherAnswer(null);
                    answer.setOverallProgress(0);
                    answer.setTimeSpent(0);
                }
            } else {
                // 设置空的答案数据
                answer.setPrecipitationAnswer(null);
                answer.setWeatherAnswer(null);
                answer.setOverallProgress(0);
                answer.setTimeSpent(0);
            }
        } else {
            // 检查权限
            if (!currentUserId.equals(answer.getUserId())) {
                throw new RuntimeException("无权限提交此考试");
            }
        }

        // 检查是否已提交
        if (answer.getAnswerStatus() != null && answer.getAnswerStatus() == 1) {
            throw new RuntimeException("考试已提交，不能重复提交");
        }

        // 更新提交信息
        answer.setAnswerStatus(1); // 已提交
        answer.setOverallProgress(reqDTO.getFinalProgress());
        answer.setTimeSpent(reqDTO.getTotalTimeSpent());
        answer.setUpdateTime(now);

        // 解析提交时间
        if (StringUtils.hasText(reqDTO.getSubmitTime())) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                answer.setSubmitTime(sdf.parse(reqDTO.getSubmitTime()));
            } catch (Exception e) {
                log.warn("解析提交时间失败: {}", reqDTO.getSubmitTime(), e);
                answer.setSubmitTime(now);
            }
        } else {
            answer.setSubmitTime(now);
        }

        boolean result;
        if (answer.getId() == null) {
            // 新记录，使用save
            result = this.save(answer);
        } else {
            // 已存在记录，使用updateById
            result = this.updateById(answer);
        }

        log.info("提交历史个例考试成功，用户ID: {}, 考试ID: {}, 题目ID: {}, 答案ID: {}",
                currentUserId, reqDTO.getExamId(), reqDTO.getQuestionId(), answer.getId());

        // ==================== 自动判卷逻辑 ====================
        if (result && answer.getId() != null) {
            try {
                // 检查是否启用自动判卷
                boolean autoGradingEnabled = gradingConfigService.isAutoGradingEnabled();
                log.info("自动判卷配置状态：{}，答案ID：{}", autoGradingEnabled, answer.getId());
                
                if (autoGradingEnabled) {
                    // 执行自动判卷
                    performAutoGrading(answer);
                } else {
                    log.info("自动判卷已禁用，跳过判卷，答案ID：{}", answer.getId());
                }
            } catch (Exception e) {
                // 自动判卷失败不应影响考试提交成功
                log.error("自动判卷失败，但考试提交成功，答案ID：{}，错误：{}", answer.getId(), e.getMessage(), e);
            }
        }

        return result;
    }

    @Override
    public WeatherHistoryExamAnswerDTO getProgress(WeatherHistoryExamAnswerReqDTO reqDTO) {
        // 获取进度信息与获取答案信息相同
        return getAnswer(reqDTO);
    }

    // ==================== 自动判卷私有方法 ====================

    /**
     * 执行自动判卷
     *
     * @param answer 已提交的答案记录
     */
    private void performAutoGrading(WeatherHistoryExamAnswer answer) {
        log.info("开始执行自动判卷，答案ID：{}", answer.getId());

        try {
            // 获取默认评分配置ID
            String defaultConfigId = gradingConfigService.getDefaultScoringConfigId();
            
            // 调用评分引擎计算得分
            ScoringEngineResult scoringResult = scoringEngine.calculateSingleScore(
                answer.getId(), 
                defaultConfigId
            );

            if (scoringResult.isSuccess()) {
                log.info("自动判卷成功，答案ID：{}，得分：{}，耗时：{}ms", 
                        answer.getId(), scoringResult.getScore(), scoringResult.getElapsedTime());
                
                // 评分成功的详细信息已经在评分引擎中更新到数据库
                // 这里可以添加额外的成功后处理逻辑，比如发送通知等
                
            } else {
                log.warn("自动判卷失败，答案ID：{}，错误信息：{}", 
                        answer.getId(), scoringResult.getMessage());
                
                // 可以在这里记录失败信息，或者设置重试逻辑
                recordGradingFailure(answer.getId(), scoringResult.getMessage());
            }

        } catch (Exception e) {
            log.error("执行自动判卷异常，答案ID：{}", answer.getId(), e);
            recordGradingFailure(answer.getId(), "自动判卷执行异常：" + e.getMessage());
            
            // 重新抛出异常，让上层处理
            throw e;
        }
    }

    /**
     * 记录判卷失败信息
     *
     * @param answerId 答案ID
     * @param errorMessage 错误信息
     */
    private void recordGradingFailure(String answerId, String errorMessage) {
        try {
            // 创建失败记录的数据
            Map<String, Object> failureInfo = new HashMap<>();
            failureInfo.put("failureTime", new Date());
            failureInfo.put("errorMessage", errorMessage);
            failureInfo.put("autoGrading", true);
            
            // 可以在这里将失败信息存储到数据库或日志系统
            // 目前先记录到日志中
            log.warn("判卷失败记录，答案ID：{}，失败信息：{}", answerId, JSON.toJSONString(failureInfo));
            
        } catch (Exception e) {
            log.error("记录判卷失败信息异常，答案ID：{}", answerId, e);
        }
    }
}
