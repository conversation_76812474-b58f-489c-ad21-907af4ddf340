#!/bin/bash

# Ubuntu环境下文件上传目录设置脚本

echo "=== 设置文件上传目录 ==="

# 1. 创建上传目录
echo "创建上传目录..."
sudo mkdir -p /data/upload/weather/micaps
sudo mkdir -p /data/upload/weather/observation
sudo mkdir -p /data/upload/weather/data
sudo mkdir -p /data/upload/weather/precipitation-area

# 2. 设置目录权限
echo "设置目录权限..."
sudo chown -R $USER:$USER /data/upload
sudo chmod -R 755 /data/upload

# 3. 检查目录是否创建成功
echo "检查目录结构..."
ls -la /data/upload/weather/

# 4. 测试写入权限
echo "测试写入权限..."
echo "test" > /data/upload/weather/test.txt
if [ $? -eq 0 ]; then
    echo "✅ 写入权限正常"
    rm /data/upload/weather/test.txt
else
    echo "❌ 写入权限异常，请检查权限设置"
fi

echo "=== 目录设置完成 ==="
